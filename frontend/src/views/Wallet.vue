<template>
  <div class="container mt-4">
    <div class="row">
      <!-- 钱包余额卡片 -->
      <div class="col-md-12 mb-4">
        <div class="card shadow wallet-balance-card">
          <div class="card-body text-center py-5">
            <div class="wallet-icon mb-3">
              <i class="bi bi-wallet2 display-1 text-primary"></i>
            </div>
            <h3 class="mb-1">钱包余额</h3>
            <div class="balance-display mb-4">
              <span class="balance-amount">{{ formattedBalance }}</span>
              <span class="balance-unit">ETH</span>
            </div>
            <div class="balance-actions">
              <button 
                class="btn btn-success btn-lg me-3"
                @click="openRechargeModal"
                :disabled="!metamaskStore.isConnected"
              >
                <i class="bi bi-plus-circle me-2"></i>
                充值
              </button>
              <button 
                class="btn btn-warning btn-lg"
                @click="openWithdrawModal"
                :disabled="!metamaskStore.isConnected || balance <= 0"
              >
                <i class="bi bi-dash-circle me-2"></i>
                提现
              </button>
            </div>
            <div v-if="!metamaskStore.isConnected" class="alert alert-info mt-3">
              <i class="bi bi-info-circle me-2"></i>
              请先连接您的 MetaMask 钱包以查看余额
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷操作卡片 -->
      <div class="col-md-4 mb-4">
        <div class="card shadow h-100">
          <div class="card-body text-center">
            <i class="bi bi-clock-history display-6 text-info mb-3"></i>
            <h5>交易记录</h5>
            <p class="text-muted small">查看详细的收支记录</p>
            <button class="btn btn-outline-info" @click="showTransactionHistory = true">
              查看记录
            </button>
          </div>
        </div>
      </div>

      <div class="col-md-4 mb-4">
        <div class="card shadow h-100">
          <div class="card-body text-center">
            <i class="bi bi-shield-check display-6 text-success mb-3"></i>
            <h5>安全设置</h5>
            <p class="text-muted small">管理钱包安全选项</p>
            <button class="btn btn-outline-success" disabled>
              敬请期待
            </button>
          </div>
        </div>
      </div>

      <div class="col-md-4 mb-4">
        <div class="card shadow h-100">
          <div class="card-body text-center">
            <i class="bi bi-graph-up display-6 text-warning mb-3"></i>
            <h5>资产统计</h5>
            <p class="text-muted small">查看资产变化趋势</p>
            <button class="btn btn-outline-warning" disabled>
              敬请期待
            </button>
          </div>
        </div>
      </div>

      <!-- 最近交易 -->
      <div class="col-md-12">
        <div class="card shadow">
          <div class="card-header bg-light">
            <h5 class="mb-0">
              <i class="bi bi-list-ul me-2"></i>
              最近交易
            </h5>
          </div>
          <div class="card-body">
            <div v-if="recentTransactions.length === 0" class="text-center py-4 text-muted">
              <i class="bi bi-inbox display-6 mb-3"></i>
              <p>暂无交易记录</p>
            </div>
            <div v-else>
              <div 
                v-for="transaction in recentTransactions" 
                :key="transaction.id"
                class="transaction-item d-flex justify-content-between align-items-center py-3 border-bottom"
              >
                <div class="d-flex align-items-center">
                  <div class="transaction-icon me-3">
                    <i 
                      :class="[
                        'bi', 
                        transaction.type === 'income' ? 'bi-arrow-down-circle text-success' : 'bi-arrow-up-circle text-danger'
                      ]"
                      style="font-size: 1.5rem;"
                    ></i>
                  </div>
                  <div>
                    <div class="fw-bold">{{ transaction.description }}</div>
                    <small class="text-muted">{{ transaction.time }}</small>
                  </div>
                </div>
                <div class="text-end">
                  <div 
                    :class="[
                      'fw-bold',
                      transaction.type === 'income' ? 'text-success' : 'text-danger'
                    ]"
                  >
                    {{ transaction.type === 'income' ? '+' : '-' }}{{ transaction.amount }} ETH
                  </div>
                  <small class="text-muted">{{ transaction.status }}</small>
                </div>
              </div>
              <div class="text-center mt-3">
                <button class="btn btn-outline-primary" @click="showTransactionHistory = true">
                  查看更多交易记录
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 充值模态框 -->
    <RechargeModal 
      :show="showRechargeModal"
      @close="showRechargeModal = false"
      @success="handleRechargeSuccess"
    />

    <!-- 提现模态框 -->
    <WithdrawModal 
      :show="showWithdrawModal"
      :max-amount="balance"
      @close="showWithdrawModal = false"
      @success="handleWithdrawSuccess"
    />

    <!-- 交易历史模态框 -->
    <TransactionHistory 
      :show="showTransactionHistory"
      @close="showTransactionHistory = false"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useMetaMaskStore } from '@/stores/metamask'
import RechargeModal from '@/components/RechargeModal.vue'
import WithdrawModal from '@/components/WithdrawModal.vue'
import TransactionHistory from '@/components/TransactionHistory.vue'

export default {
  name: 'Wallet',
  components: {
    RechargeModal,
    WithdrawModal,
    TransactionHistory
  },
  setup() {
    const metamaskStore = useMetaMaskStore()
    
    const balance = ref(0)
    const showRechargeModal = ref(false)
    const showWithdrawModal = ref(false)
    const showTransactionHistory = ref(false)
    const recentTransactions = ref([])

    const formattedBalance = computed(() => {
      return parseFloat(balance.value).toFixed(4)
    })

    // 加载钱包余额
    const loadBalance = async () => {
      if (!metamaskStore.isConnected) return
      
      try {
        // TODO: 调用智能合约获取余额
        // const contract = new metamaskStore.web3.eth.Contract(ABI, CONTRACT_ADDRESS)
        // const result = await contract.methods.getBalance(metamaskStore.currentAccount).call()
        
        // 模拟从智能合约获取余额
        balance.value = parseFloat(metamaskStore.balance) || 0
      } catch (error) {
        console.error('获取余额失败:', error)
      }
    }

    // 加载最近交易
    const loadRecentTransactions = async () => {
      if (!metamaskStore.currentAccount) return
      
      try {
        // TODO: 调用 API 获取交易记录
        // const response = await fetch(`/api/transactions/${metamaskStore.currentAccount}?limit=5`)
        // const data = await response.json()
        
        // 模拟交易数据
        recentTransactions.value = [
          {
            id: 1,
            type: 'income',
            description: '专利出售收入 - 智能家居控制系统',
            amount: '5.2500',
            time: '2024-01-15 14:30:25',
            status: '已确认'
          },
          {
            id: 2,
            type: 'expense',
            description: '购买专利 - 区块链数据存储',
            amount: '2.1000',
            time: '2024-01-14 09:15:42',
            status: '已确认'
          },
          {
            id: 3,
            type: 'income',
            description: '钱包充值',
            amount: '10.0000',
            time: '2024-01-13 16:45:18',
            status: '已确认'
          }
        ]
      } catch (error) {
        console.error('加载交易记录失败:', error)
      }
    }

    // 打开充值模态框
    const openRechargeModal = () => {
      showRechargeModal.value = true
    }

    // 打开提现模态框
    const openWithdrawModal = () => {
      showWithdrawModal.value = true
    }

    // 处理充值成功
    const handleRechargeSuccess = (amount) => {
      balance.value += parseFloat(amount)
      loadRecentTransactions() // 刷新交易记录
    }

    // 处理提现成功
    const handleWithdrawSuccess = (amount) => {
      balance.value -= parseFloat(amount)
      loadRecentTransactions() // 刷新交易记录
    }

    // 监听钱包连接状态
    watch(() => metamaskStore.isConnected, (connected) => {
      if (connected) {
        loadBalance()
        loadRecentTransactions()
      } else {
        balance.value = 0
        recentTransactions.value = []
      }
    })

    // 监听账户变化
    watch(() => metamaskStore.currentAccount, () => {
      if (metamaskStore.currentAccount) {
        loadBalance()
        loadRecentTransactions()
      }
    })

    onMounted(() => {
      if (metamaskStore.isConnected) {
        loadBalance()
        loadRecentTransactions()
      }
    })

    return {
      metamaskStore,
      balance,
      formattedBalance,
      showRechargeModal,
      showWithdrawModal,
      showTransactionHistory,
      recentTransactions,
      openRechargeModal,
      openWithdrawModal,
      handleRechargeSuccess,
      handleWithdrawSuccess
    }
  }
}
</script>

<style scoped>
.wallet-balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20px;
}

.wallet-icon {
  opacity: 0.8;
}

.balance-display {
  font-family: 'Courier New', monospace;
}

.balance-amount {
  font-size: 3rem;
  font-weight: bold;
  margin-right: 0.5rem;
}

.balance-unit {
  font-size: 1.5rem;
  opacity: 0.8;
}

.balance-actions .btn {
  border-radius: 25px;
  padding: 10px 30px;
  font-weight: bold;
}

.card {
  border: none;
  border-radius: 15px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.transaction-item {
  transition: background-color 0.2s ease;
}

.transaction-item:hover {
  background-color: #f8f9fa;
  border-radius: 8px;
}

.transaction-item:last-child {
  border-bottom: none !important;
}

.alert {
  border-radius: 10px;
  border: none;
}

.btn {
  border-radius: 8px;
  font-weight: 500;
}

.btn:hover {
  transform: translateY(-1px);
}

.display-6 {
  opacity: 0.7;
}

@media (max-width: 768px) {
  .balance-amount {
    font-size: 2rem;
  }
  
  .balance-unit {
    font-size: 1rem;
  }
  
  .balance-actions .btn {
    padding: 8px 20px;
    margin: 0.25rem;
  }
}
</style> 