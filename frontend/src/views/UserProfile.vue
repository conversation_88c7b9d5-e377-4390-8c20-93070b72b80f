<template>
  <div class="container mt-4">
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="card shadow">
          <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
              <i class="bi bi-person-circle me-2"></i>
              个人资料
            </h4>
          </div>
          <div class="card-body">
            <!-- 区块链地址显示 -->
            <div class="mb-4">
              <label class="form-label fw-bold">区块链地址</label>
              <div class="input-group">
                <input 
                  type="text" 
                  class="form-control" 
                  :value="metamaskStore.currentAccount" 
                  readonly
                  style="background-color: #f8f9fa;"
                >
                <button 
                  class="btn btn-outline-secondary" 
                  type="button"
                  @click="copyAddress"
                  :disabled="!metamaskStore.currentAccount"
                >
                  <i class="bi bi-clipboard"></i>
                </button>
              </div>
              <small class="text-muted">此地址不可修改，来自您的 MetaMask 钱包</small>
            </div>

            <!-- 个人信息表单 -->
            <form @submit.prevent="saveProfile">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="realName" class="form-label">
                    真实姓名 <span class="text-danger">*</span>
                  </label>
                  <input 
                    type="text" 
                    class="form-control" 
                    id="realName"
                    v-model="profileForm.realName"
                    :class="{ 'is-invalid': errors.realName }"
                    placeholder="请输入真实姓名"
                    required
                  >
                  <div v-if="errors.realName" class="invalid-feedback">
                    {{ errors.realName }}
                  </div>
                </div>
                
                <div class="col-md-6 mb-3">
                  <label for="phone" class="form-label">
                    手机号码 <span class="text-danger">*</span>
                  </label>
                  <input 
                    type="tel" 
                    class="form-control" 
                    id="phone"
                    v-model="profileForm.phone"
                    :class="{ 'is-invalid': errors.phone }"
                    placeholder="请输入手机号码"
                    required
                  >
                  <div v-if="errors.phone" class="invalid-feedback">
                    {{ errors.phone }}
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label for="idCard" class="form-label">
                  身份证号码 <span class="text-danger">*</span>
                </label>
                <input 
                  type="text" 
                  class="form-control" 
                  id="idCard"
                  v-model="profileForm.idCard"
                  :class="{ 'is-invalid': errors.idCard }"
                  placeholder="请输入身份证号码"
                  required
                >
                <div v-if="errors.idCard" class="invalid-feedback">
                  {{ errors.idCard }}
                </div>
              </div>

              <div class="mb-3">
                <label for="email" class="form-label">邮箱地址</label>
                <input 
                  type="email" 
                  class="form-control" 
                  id="email"
                  v-model="profileForm.email"
                  :class="{ 'is-invalid': errors.email }"
                  placeholder="请输入邮箱地址（可选）"
                >
                <div v-if="errors.email" class="invalid-feedback">
                  {{ errors.email }}
                </div>
              </div>

              <!-- 区块链确认提示 -->
              <div v-if="needsBlockchainConfirmation" class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>区块链确认：</strong>
                更新个人信息需要区块链交易确认，请确保您的钱包有足够的 ETH 用于支付 Gas 费用。
              </div>

              <!-- 提交按钮 -->
              <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button 
                  type="button" 
                  class="btn btn-secondary me-md-2"
                  @click="resetForm"
                  :disabled="loading"
                >
                  重置
                </button>
                <button 
                  type="submit" 
                  class="btn btn-primary"
                  :disabled="loading || !metamaskStore.isConnected"
                >
                  <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                  {{ loading ? '保存中...' : '保存资料' }}
                </button>
              </div>
            </form>

            <!-- 连接钱包提示 -->
            <div v-if="!metamaskStore.isConnected" class="alert alert-info mt-3">
              <i class="bi bi-info-circle me-2"></i>
              请先连接您的 MetaMask 钱包以管理个人资料。
            </div>
          </div>
        </div>

        <!-- 资料完成度提示 -->
        <div v-if="isFirstTimeUser" class="alert alert-warning mt-3">
          <i class="bi bi-exclamation-triangle me-2"></i>
          <strong>完善资料：</strong>
          您是首次使用用户，请完善个人信息后才能使用完整功能。
        </div>
      </div>
    </div>

    <!-- 成功/错误提示模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" :class="messageType === 'success' ? 'bg-success text-white' : 'bg-danger text-white'">
            <h5 class="modal-title">
              <i :class="messageType === 'success' ? 'bi bi-check-circle' : 'bi bi-exclamation-triangle'" class="me-2"></i>
              {{ messageType === 'success' ? '操作成功' : '操作失败' }}
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            {{ message }}
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">确定</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useMetaMaskStore } from '@/stores/metamask'

export default {
  name: 'UserProfile',
  setup() {
    const metamaskStore = useMetaMaskStore()
    
    const loading = ref(false)
    const message = ref('')
    const messageType = ref('success')
    const isFirstTimeUser = ref(false)
    
    const profileForm = reactive({
      realName: '',
      phone: '',
      idCard: '',
      email: ''
    })
    
    const errors = reactive({
      realName: '',
      phone: '',
      idCard: '',
      email: ''
    })

    const needsBlockchainConfirmation = computed(() => {
      return profileForm.realName || profileForm.phone || profileForm.idCard
    })

    // 验证表单
    const validateForm = () => {
      clearErrors()
      let isValid = true
      
      if (!profileForm.realName.trim()) {
        errors.realName = '请输入真实姓名'
        isValid = false
      }
      
      if (!profileForm.phone.trim()) {
        errors.phone = '请输入手机号码'
        isValid = false
      } else if (!/^1[3-9]\d{9}$/.test(profileForm.phone)) {
        errors.phone = '请输入有效的手机号码'
        isValid = false
      }
      
      if (!profileForm.idCard.trim()) {
        errors.idCard = '请输入身份证号码'
        isValid = false
      } else if (!/^\d{15}$|^\d{17}[\dxX]$/.test(profileForm.idCard)) {
        errors.idCard = '请输入有效的身份证号码'
        isValid = false
      }
      
      if (profileForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profileForm.email)) {
        errors.email = '请输入有效的邮箱地址'
        isValid = false
      }
      
      return isValid
    }

    const clearErrors = () => {
      Object.keys(errors).forEach(key => {
        errors[key] = ''
      })
    }

    // 复制地址到剪贴板
    const copyAddress = async () => {
      try {
        await navigator.clipboard.writeText(metamaskStore.currentAccount)
        showMessage('地址已复制到剪贴板', 'success')
      } catch (err) {
        showMessage('复制失败，请手动复制', 'error')
      }
    }

    // 显示消息
    const showMessage = (msg, type = 'success') => {
      message.value = msg
      messageType.value = type
      const modal = new bootstrap.Modal(document.getElementById('messageModal'))
      modal.show()
    }

    // 加载用户资料
    const loadProfile = async () => {
      if (!metamaskStore.currentAccount) return
      
      try {
        // TODO: 调用 API 获取用户资料
        // const response = await fetch(`/api/profile/${metamaskStore.currentAccount}`)
        // const data = await response.json()
        
        // 模拟数据
        const mockData = {
          realName: '',
          phone: '',
          idCard: '',
          email: '',
          isFirstTime: true
        }
        
        Object.assign(profileForm, mockData)
        isFirstTimeUser.value = mockData.isFirstTime
      } catch (error) {
        console.error('加载用户资料失败:', error)
        showMessage('加载用户资料失败', 'error')
      }
    }

    // 保存资料
    const saveProfile = async () => {
      if (!validateForm()) return
      
      loading.value = true
      
      try {
        // 模拟区块链交易确认
        if (needsBlockchainConfirmation.value) {
          // TODO: 调用智能合约更新信息
          await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟等待
        }
        
        // TODO: 调用 API 保存用户资料
        // const response = await fetch('/api/profile', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({
        //     address: metamaskStore.currentAccount,
        //     ...profileForm
        //   })
        // })
        
        showMessage('个人资料保存成功', 'success')
        isFirstTimeUser.value = false
      } catch (error) {
        console.error('保存失败:', error)
        showMessage('保存失败，请重试', 'error')
      } finally {
        loading.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      Object.assign(profileForm, {
        realName: '',
        phone: '',
        idCard: '',
        email: ''
      })
      clearErrors()
    }

    onMounted(() => {
      loadProfile()
    })

    return {
      metamaskStore,
      loading,
      message,
      messageType,
      isFirstTimeUser,
      profileForm,
      errors,
      needsBlockchainConfirmation,
      copyAddress,
      saveProfile,
      resetForm
    }
  }
}
</script>

<style scoped>
.card {
  border: none;
  border-radius: 15px;
}

.card-header {
  border-radius: 15px 15px 0 0 !important;
}

.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
  background: linear-gradient(45deg, #0d6efd, #6f42c1);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #0b5ed7, #5a32a3);
}

.alert {
  border-radius: 10px;
}

.modal-content {
  border-radius: 15px;
}
</style> 